package com.multiplier.timeoff.core.util;

import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import com.multiplier.timeoff.repository.TimeoffEntryRepository;
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import com.multiplier.timeoff.types.TimeOffEntryType;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j(topic = "WorkshiftUtil")
public class WorkshiftUtil {

    private final static WorkshiftDTO.WorkingHoursDTO DEFAULT_WORKHOURS = WorkshiftDTO.WorkingHoursDTO.builder()
        .startTime(LocalTime.of(9, 0))
        .endTime(LocalTime.of(18, 0))
        .build();

    private final static WorkshiftDTO.WorkingHoursDTO DEFAULT_BREAKHOURS = WorkshiftDTO.WorkingHoursDTO.builder()
        .startTime(LocalTime.of(13, 0))
        .endTime(LocalTime.of(14, 0))
        .build();

    public final static WorkshiftDTO DEFAULT_WORKSHIFT = WorkshiftDTO.builder()
        .startDate(DayOfWeek.MONDAY)
        .endDate(DayOfWeek.FRIDAY)
        .workHours(DEFAULT_WORKHOURS)
        .breakHours(DEFAULT_BREAKHOURS)
        .build();

    public static boolean isRestDay(LocalDate date, WorkshiftDTO workshift) {
        val restDays = getRestDays(workshift);
        return restDays.contains(date.getDayOfWeek());
    }

    public static List<LocalDate> getRestDaysListForRange(LocalDate startDate, LocalDate endDate, WorkshiftDTO workshift) {
        val restDays = getRestDays(workshift);
        return startDate.datesUntil(endDate.plusDays(1)).filter(date -> restDays.contains(date.getDayOfWeek())).collect(Collectors.toList());
    }

    private static List<DayOfWeek> getRestDays(WorkshiftDTO workshift) {

        val startDate = workshift.getStartDate().getValue();
        val endDate = workshift.getEndDate().getValue();
        List<DayOfWeek> restDays = new ArrayList<>();
        EnumSet<DayOfWeek> allDays = EnumSet.allOf(DayOfWeek.class);

        for (DayOfWeek day : allDays) {
            boolean isInRange;

            // Check if the range is a normal one (e.g., Monday to Friday)
            if (startDate <= endDate) {
                isInRange = (day.getValue() >= startDate && day.getValue() <= endDate);
            } else {
                // The range wraps around the week (e.g., Friday to Tuesday)
                isInRange = (day.getValue() >= startDate || day.getValue() <= endDate);
            }

            if (!isInRange) {
                restDays.add(day);
            }
        }
        return restDays;
    }

    public static Set<LocalDate> getHolidayDates(TimeoffEntryRepository repository, Long timeoffId) {
        List<TimeoffEntryDBO> timeoffEntries = repository.findAll(
                (root, query, criteriaBuilder) -> criteriaBuilder.and(
                        criteriaBuilder.equal(root.get("timeoffId"), timeoffId),
                        criteriaBuilder.equal(root.get("type"), TimeOffEntryType.HOLIDAY)
                )
        );

        return timeoffEntries.stream()
                .map(TimeoffEntryDBO::date)
                .collect(Collectors.toSet());
    }

}
